#!/usr/bin/env node

/**
 * Enhanced Mempool Streamer Test Suite
 * 
 * Validates the modern mempool implementation using Ethers.js and Viem
 * Based on QuickNode best practices
 */

import dotenv from 'dotenv';
import EnhancedMempoolStreamer from './src/phoenix/components/enhanced-mempool-streamer.js';

dotenv.config();

class EnhancedMempoolTester {
  constructor() {
    this.testResults = {
      connection: { passed: false, details: [] },
      dualProvider: { passed: false, details: [] },
      whaleDetection: { passed: false, details: [] },
      performance: { passed: false, details: [] },
      reliability: { passed: false, details: [] }
    };
    
    this.mempoolStreamer = null;
    this.testStartTime = Date.now();
    this.transactionCount = 0;
    this.whaleDetectionCount = 0;
  }

  /**
   * Run comprehensive test suite
   */
  async runTests() {
    console.log('🧪 ENHANCED MEMPOOL STREAMER TESTING STARTED\n');
    
    try {
      await this.testConnection();
      await this.testDualProviderSetup();
      await this.testWhaleDetection();
      await this.testPerformance();
      await this.testReliability();
      
      this.generateReport();
      
    } catch (error) {
      console.error('💥 TEST SUITE FAILED:', error.message);
    } finally {
      if (this.mempoolStreamer) {
        await this.mempoolStreamer.stop();
      }
    }
  }

  /**
   * Test basic connection functionality
   */
  async testConnection() {
    console.log('🔌 Testing Connection Functionality...');
    
    try {
      // Create enhanced mempool streamer
      this.mempoolStreamer = new EnhancedMempoolStreamer({
        symbol: 'ETH',
        enableRealTimeFeeds: true,
        whaleThreshold: 50, // Lower threshold for testing
        logger: {
          info: (key, data) => console.log(`[INFO] ${key}:`, JSON.stringify(data)),
          warn: (key, data) => console.warn(`[WARN] ${key}:`, JSON.stringify(data)),
          error: (key, data) => console.error(`[ERROR] ${key}:`, JSON.stringify(data)),
          debug: (key, data) => console.log(`[DEBUG] ${key}:`, JSON.stringify(data))
        }
      });

      // Test initialization
      const started = await this.mempoolStreamer.start();
      
      if (started) {
        this.testResults.connection.details.push('✅ Enhanced mempool streamer started successfully');
        this.testResults.connection.passed = true;
      } else {
        this.testResults.connection.details.push('❌ Failed to start enhanced mempool streamer');
      }

      // Wait for initial connection establishment
      await new Promise(resolve => setTimeout(resolve, 3000));

      const stats = this.mempoolStreamer.getStats();
      if (stats.activeConnections > 0) {
        this.testResults.connection.details.push(`✅ Active connections: ${stats.activeConnections}`);
      } else {
        this.testResults.connection.details.push('❌ No active connections established');
      }

    } catch (error) {
      this.testResults.connection.details.push(`❌ Connection error: ${error.message}`);
    }
    
    console.log(`   Result: ${this.testResults.connection.passed ? 'PASSED' : 'FAILED'}\n`);
  }

  /**
   * Test dual provider setup (Ethers.js + Viem)
   */
  async testDualProviderSetup() {
    console.log('🔄 Testing Dual Provider Setup...');
    
    try {
      if (!this.mempoolStreamer) {
        this.testResults.dualProvider.details.push('❌ Mempool streamer not initialized');
        return;
      }

      const connections = this.mempoolStreamer.activeConnections;
      
      // Check for Ethers.js provider
      if (connections.has('ethers')) {
        this.testResults.dualProvider.details.push('✅ Ethers.js provider connected');
      } else {
        this.testResults.dualProvider.details.push('❌ Ethers.js provider not connected');
      }

      // Check for Viem provider
      if (connections.has('viem')) {
        this.testResults.dualProvider.details.push('✅ Viem provider connected');
      } else {
        this.testResults.dualProvider.details.push('⚠️ Viem provider not connected (backup may be unavailable)');
      }

      // Test provider redundancy
      if (connections.size >= 1) {
        this.testResults.dualProvider.details.push(`✅ Provider redundancy: ${connections.size} active connections`);
        this.testResults.dualProvider.passed = true;
      } else {
        this.testResults.dualProvider.details.push('❌ No provider redundancy');
      }

    } catch (error) {
      this.testResults.dualProvider.details.push(`❌ Dual provider test error: ${error.message}`);
    }
    
    console.log(`   Result: ${this.testResults.dualProvider.passed ? 'PASSED' : 'FAILED'}\n`);
  }

  /**
   * Test whale detection functionality
   */
  async testWhaleDetection() {
    console.log('🐋 Testing Whale Detection...');
    
    try {
      if (!this.mempoolStreamer) {
        this.testResults.whaleDetection.details.push('❌ Mempool streamer not initialized');
        return;
      }

      // Set up whale detection listener
      const whaleDetectionPromise = new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve({ detected: false, reason: 'timeout' });
        }, 30000); // 30 second timeout

        this.mempoolStreamer.on('WHALE_INTENT_DETECTED', (intent) => {
          clearTimeout(timeout);
          this.whaleDetectionCount++;
          resolve({ detected: true, intent });
        });
      });

      // Monitor transaction processing
      const initialStats = this.mempoolStreamer.getStats();
      
      // Wait for whale detection or timeout
      const result = await whaleDetectionPromise;
      
      const finalStats = this.mempoolStreamer.getStats();
      const transactionsProcessed = finalStats.totalTransactions - initialStats.totalTransactions;
      
      this.testResults.whaleDetection.details.push(`📊 Transactions processed: ${transactionsProcessed}`);
      
      if (result.detected) {
        this.testResults.whaleDetection.details.push('✅ Whale intent detection working');
        this.testResults.whaleDetection.details.push(`🎯 Detected: ${result.intent.whaleAddress} ($${result.intent.estimatedValue.toLocaleString()})`);
        this.testResults.whaleDetection.passed = true;
      } else {
        if (transactionsProcessed > 0) {
          this.testResults.whaleDetection.details.push('⚠️ Transaction processing working, but no whale activity detected');
          this.testResults.whaleDetection.details.push('💡 This may be normal if no whale transactions occurred during test');
          this.testResults.whaleDetection.passed = true; // Pass if transactions are being processed
        } else {
          this.testResults.whaleDetection.details.push('❌ No transactions processed - connection may be inactive');
        }
      }

    } catch (error) {
      this.testResults.whaleDetection.details.push(`❌ Whale detection test error: ${error.message}`);
    }
    
    console.log(`   Result: ${this.testResults.whaleDetection.passed ? 'PASSED' : 'FAILED'}\n`);
  }

  /**
   * Test performance metrics
   */
  async testPerformance() {
    console.log('⚡ Testing Performance...');
    
    try {
      if (!this.mempoolStreamer) {
        this.testResults.performance.details.push('❌ Mempool streamer not initialized');
        return;
      }

      const stats = this.mempoolStreamer.getStats();
      
      // Test transaction processing rate
      if (stats.totalTransactions > 0) {
        this.testResults.performance.details.push(`✅ Transactions processed: ${stats.totalTransactions}`);
        this.testResults.performance.details.push(`📈 Rate: ${stats.transactionsPerSecond} tx/sec`);
      } else {
        this.testResults.performance.details.push('⚠️ No transactions processed yet');
      }

      // Test connection uptime
      if (stats.uptime > 0) {
        this.testResults.performance.details.push(`⏱️ Uptime: ${stats.uptime} seconds`);
      }

      // Test whale detection rate
      if (stats.whaleTransactions > 0) {
        this.testResults.performance.details.push(`🐋 Whale detection rate: ${stats.whaleDetectionRate}`);
      }

      // Performance criteria
      const hasActiveConnections = stats.activeConnections > 0;
      const hasUptime = stats.uptime > 10; // At least 10 seconds uptime
      
      if (hasActiveConnections && hasUptime) {
        this.testResults.performance.passed = true;
        this.testResults.performance.details.push('✅ Performance metrics within acceptable range');
      } else {
        this.testResults.performance.details.push('❌ Performance metrics below threshold');
      }

    } catch (error) {
      this.testResults.performance.details.push(`❌ Performance test error: ${error.message}`);
    }
    
    console.log(`   Result: ${this.testResults.performance.passed ? 'PASSED' : 'FAILED'}\n`);
  }

  /**
   * Test reliability and error handling
   */
  async testReliability() {
    console.log('🛡️ Testing Reliability...');
    
    try {
      if (!this.mempoolStreamer) {
        this.testResults.reliability.details.push('❌ Mempool streamer not initialized');
        return;
      }

      // Test connection monitoring
      if (this.mempoolStreamer.connectionMonitorInterval) {
        this.testResults.reliability.details.push('✅ Connection monitoring active');
      } else {
        this.testResults.reliability.details.push('❌ Connection monitoring not active');
      }

      // Test reconnection methods
      if (typeof this.mempoolStreamer.attemptReconnection === 'function') {
        this.testResults.reliability.details.push('✅ Reconnection mechanism implemented');
      } else {
        this.testResults.reliability.details.push('❌ Reconnection mechanism missing');
      }

      // Test error handling
      if (typeof this.mempoolStreamer.handleProviderError === 'function') {
        this.testResults.reliability.details.push('✅ Error handling implemented');
      } else {
        this.testResults.reliability.details.push('❌ Error handling missing');
      }

      // Test graceful shutdown
      if (typeof this.mempoolStreamer.stop === 'function') {
        this.testResults.reliability.details.push('✅ Graceful shutdown method available');
        this.testResults.reliability.passed = true;
      } else {
        this.testResults.reliability.details.push('❌ Graceful shutdown method missing');
      }

    } catch (error) {
      this.testResults.reliability.details.push(`❌ Reliability test error: ${error.message}`);
    }
    
    console.log(`   Result: ${this.testResults.reliability.passed ? 'PASSED' : 'FAILED'}\n`);
  }

  /**
   * Generate comprehensive test report
   */
  generateReport() {
    const totalTime = Date.now() - this.testStartTime;
    const passedTests = Object.values(this.testResults).filter(result => result.passed).length;
    const totalTests = Object.keys(this.testResults).length;
    
    console.log('='.repeat(80));
    console.log('ENHANCED MEMPOOL STREAMER TEST REPORT');
    console.log('='.repeat(80));
    console.log(`📊 Overall Result: ${passedTests}/${totalTests} tests passed`);
    console.log(`⏱️ Total Test Time: ${Math.round(totalTime / 1000)}s`);
    console.log('');
    
    // Detailed results
    Object.entries(this.testResults).forEach(([testName, result]) => {
      const status = result.passed ? '✅ PASSED' : '❌ FAILED';
      console.log(`${testName.toUpperCase()}: ${status}`);
      result.details.forEach(detail => console.log(`   ${detail}`));
      console.log('');
    });
    
    // Final assessment
    if (passedTests === totalTests) {
      console.log('🎉 ALL TESTS PASSED - ENHANCED MEMPOOL STREAMER READY!');
      console.log('🚀 Modern implementation with Ethers.js and Viem validated');
    } else {
      console.log('⚠️ SOME TESTS FAILED - REVIEW REQUIRED');
      console.log('🔧 Check failed tests before production deployment');
    }
    
    // Performance summary
    if (this.mempoolStreamer) {
      const finalStats = this.mempoolStreamer.getStats();
      console.log('\n📈 PERFORMANCE SUMMARY:');
      console.log(`   Transactions: ${finalStats.totalTransactions}`);
      console.log(`   Whale Detections: ${finalStats.whaleTransactions}`);
      console.log(`   Active Connections: ${finalStats.activeConnections}`);
      console.log(`   Uptime: ${finalStats.uptime}s`);
    }
    
    console.log('='.repeat(80));
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new EnhancedMempoolTester();
  tester.runTests().catch(console.error);
}

export default EnhancedMempoolTester;
