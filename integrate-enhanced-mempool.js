#!/usr/bin/env node

/**
 * Enhanced Mempool Integration Script
 * 
 * Seamlessly integrates the enhanced mempool streamer into the Phoenix Engine
 * Provides backward compatibility and migration path
 */

import fs from 'fs';
import path from 'path';

class MempoolIntegrator {
  constructor() {
    this.backupSuffix = '.backup-' + Date.now();
    this.integrationSteps = [
      'Backup existing mempool streamer',
      'Update Phoenix Engine imports',
      'Modify component initialization',
      'Update configuration handling',
      'Validate integration'
    ];
    this.completedSteps = [];
  }

  /**
   * Run complete integration process
   */
  async integrate() {
    console.log('🔄 ENHANCED MEMPOOL INTEGRATION STARTING...\n');
    
    try {
      await this.backupExistingFiles();
      await this.updatePhoenixEngine();
      await this.updateConfiguration();
      await this.validateIntegration();
      
      this.generateIntegrationReport();
      
    } catch (error) {
      console.error('💥 INTEGRATION FAILED:', error.message);
      await this.rollbackChanges();
    }
  }

  /**
   * Backup existing mempool streamer
   */
  async backupExistingFiles() {
    console.log('📦 Step 1: Backing up existing files...');
    
    try {
      const filesToBackup = [
        'src/phoenix/engine.js',
        'src/phoenix/components/mempool-streamer.js'
      ];

      for (const file of filesToBackup) {
        if (fs.existsSync(file)) {
          const backupPath = file + this.backupSuffix;
          fs.copyFileSync(file, backupPath);
          console.log(`   ✅ Backed up: ${file} → ${backupPath}`);
        }
      }

      this.completedSteps.push('Backup existing mempool streamer');
      
    } catch (error) {
      throw new Error(`Backup failed: ${error.message}`);
    }
  }

  /**
   * Update Phoenix Engine to use enhanced mempool streamer
   */
  async updatePhoenixEngine() {
    console.log('🔧 Step 2: Updating Phoenix Engine...');
    
    try {
      const enginePath = 'src/phoenix/engine.js';
      let engineContent = fs.readFileSync(enginePath, 'utf8');

      // Update import statement
      const oldImport = "import MempoolStreamer from './components/mempool-streamer.js';";
      const newImport = "import EnhancedMempoolStreamer from './components/enhanced-mempool-streamer.js';";
      
      if (engineContent.includes(oldImport)) {
        engineContent = engineContent.replace(oldImport, newImport);
        console.log('   ✅ Updated import statement');
      } else {
        // Add import if not present
        const importSection = engineContent.match(/import.*from.*\.js';/g);
        if (importSection) {
          const lastImport = importSection[importSection.length - 1];
          engineContent = engineContent.replace(lastImport, lastImport + '\n' + newImport);
          console.log('   ✅ Added enhanced mempool import');
        }
      }

      // Update component initialization
      const oldInit = 'this.mempoolStreamer = new MempoolStreamer({';
      const newInit = 'this.mempoolStreamer = new EnhancedMempoolStreamer({';
      
      if (engineContent.includes(oldInit)) {
        engineContent = engineContent.replace(oldInit, newInit);
        console.log('   ✅ Updated component initialization');
      }

      // Add enhanced configuration options
      const configPattern = /this\.mempoolStreamer = new EnhancedMempoolStreamer\(\{[\s\S]*?\}\);/;
      const match = engineContent.match(configPattern);
      
      if (match) {
        const enhancedConfig = `this.mempoolStreamer = new EnhancedMempoolStreamer({
      symbol: this.config.symbol.replace('USDT', ''),
      logger: this.logger,
      enableRealTimeFeeds: this.config.enableRealTimeFeeds,
      whaleThreshold: this.config.whaleThreshold || 100,
      providers: {
        alchemy: {
          enabled: !!process.env.ALCHEMY_WS_URL,
          wsUrl: process.env.ALCHEMY_WS_URL
        },
        quicknode: {
          enabled: !!process.env.QUICKNODE_WS_URL,
          wsUrl: process.env.QUICKNODE_WS_URL
        }
      }
    });`;
        
        engineContent = engineContent.replace(match[0], enhancedConfig);
        console.log('   ✅ Enhanced configuration options added');
      }

      // Write updated content
      fs.writeFileSync(enginePath, engineContent);
      
      this.completedSteps.push('Update Phoenix Engine imports');
      this.completedSteps.push('Modify component initialization');
      
    } catch (error) {
      throw new Error(`Phoenix Engine update failed: ${error.message}`);
    }
  }

  /**
   * Update configuration files
   */
  async updateConfiguration() {
    console.log('⚙️ Step 3: Updating configuration...');
    
    try {
      // Update .env.example with new variables
      const envExamplePath = '.env.example';
      if (fs.existsSync(envExamplePath)) {
        let envContent = fs.readFileSync(envExamplePath, 'utf8');
        
        const enhancedMempoolVars = `
# Enhanced Mempool Configuration
ALCHEMY_WS_URL=wss://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY
QUICKNODE_WS_URL=wss://your-quicknode-endpoint.quiknode.pro/YOUR_KEY/
WHALE_THRESHOLD=100
WHALE_WATCHLIST=******************************************,0x40B38765648C6F0E774A259136a07746e4F2eE5`;

        if (!envContent.includes('ALCHEMY_WS_URL')) {
          envContent += enhancedMempoolVars;
          fs.writeFileSync(envExamplePath, envContent);
          console.log('   ✅ Updated .env.example with enhanced mempool variables');
        }
      }

      // Check if .env needs updating
      const envPath = '.env';
      if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf8');
        
        const missingVars = [];
        if (!envContent.includes('ALCHEMY_WS_URL')) missingVars.push('ALCHEMY_WS_URL');
        if (!envContent.includes('QUICKNODE_WS_URL')) missingVars.push('QUICKNODE_WS_URL');
        if (!envContent.includes('WHALE_THRESHOLD')) missingVars.push('WHALE_THRESHOLD');
        
        if (missingVars.length > 0) {
          console.log(`   ⚠️ Missing environment variables: ${missingVars.join(', ')}`);
          console.log('   💡 Please add these to your .env file for full functionality');
        } else {
          console.log('   ✅ Environment variables already configured');
        }
      }

      this.completedSteps.push('Update configuration handling');
      
    } catch (error) {
      throw new Error(`Configuration update failed: ${error.message}`);
    }
  }

  /**
   * Validate integration
   */
  async validateIntegration() {
    console.log('✅ Step 4: Validating integration...');
    
    try {
      // Check if enhanced mempool streamer file exists
      const enhancedMempoolPath = 'src/phoenix/components/enhanced-mempool-streamer.js';
      if (!fs.existsSync(enhancedMempoolPath)) {
        throw new Error('Enhanced mempool streamer file not found');
      }
      console.log('   ✅ Enhanced mempool streamer file exists');

      // Check if Phoenix Engine was updated correctly
      const enginePath = 'src/phoenix/engine.js';
      const engineContent = fs.readFileSync(enginePath, 'utf8');
      
      if (engineContent.includes('EnhancedMempoolStreamer')) {
        console.log('   ✅ Phoenix Engine updated to use enhanced mempool streamer');
      } else {
        throw new Error('Phoenix Engine not properly updated');
      }

      // Syntax check
      try {
        await import('./src/phoenix/components/enhanced-mempool-streamer.js');
        console.log('   ✅ Enhanced mempool streamer syntax valid');
      } catch (syntaxError) {
        throw new Error(`Syntax validation failed: ${syntaxError.message}`);
      }

      this.completedSteps.push('Validate integration');
      
    } catch (error) {
      throw new Error(`Validation failed: ${error.message}`);
    }
  }

  /**
   * Rollback changes if integration fails
   */
  async rollbackChanges() {
    console.log('🔄 Rolling back changes...');
    
    try {
      const filesToRestore = [
        'src/phoenix/engine.js',
        'src/phoenix/components/mempool-streamer.js'
      ];

      for (const file of filesToRestore) {
        const backupPath = file + this.backupSuffix;
        if (fs.existsSync(backupPath)) {
          fs.copyFileSync(backupPath, file);
          fs.unlinkSync(backupPath);
          console.log(`   ✅ Restored: ${file}`);
        }
      }
      
      console.log('🔄 Rollback completed successfully');
      
    } catch (error) {
      console.error('💥 Rollback failed:', error.message);
    }
  }

  /**
   * Generate integration report
   */
  generateIntegrationReport() {
    console.log('\n' + '='.repeat(80));
    console.log('ENHANCED MEMPOOL INTEGRATION REPORT');
    console.log('='.repeat(80));
    
    console.log('📊 Integration Status: SUCCESS');
    console.log(`✅ Completed Steps: ${this.completedSteps.length}/${this.integrationSteps.length}`);
    console.log('');
    
    this.completedSteps.forEach((step, index) => {
      console.log(`${index + 1}. ✅ ${step}`);
    });
    
    console.log('\n🚀 NEXT STEPS:');
    console.log('1. Update your .env file with WebSocket URLs:');
    console.log('   - ALCHEMY_WS_URL=wss://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY');
    console.log('   - QUICKNODE_WS_URL=wss://your-endpoint.quiknode.pro/YOUR_KEY/');
    console.log('');
    console.log('2. Test the enhanced mempool streamer:');
    console.log('   npm run test:mempool');
    console.log('');
    console.log('3. Deploy with enhanced capabilities:');
    console.log('   node phoenix-production.js');
    console.log('');
    
    console.log('🎉 ENHANCED MEMPOOL INTEGRATION COMPLETE!');
    console.log('📈 Benefits:');
    console.log('   • Modern Ethers.js and Viem support');
    console.log('   • Dual-provider redundancy');
    console.log('   • Improved whale detection');
    console.log('   • Enhanced reliability and performance');
    console.log('');
    
    console.log('📋 Backup Files Created:');
    console.log(`   • src/phoenix/engine.js${this.backupSuffix}`);
    console.log(`   • src/phoenix/components/mempool-streamer.js${this.backupSuffix}`);
    console.log('   (Remove these after successful testing)');
    
    console.log('='.repeat(80));
  }
}

// Run integration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const integrator = new MempoolIntegrator();
  integrator.integrate().catch(console.error);
}

export default MempoolIntegrator;
