#!/usr/bin/env node

/**
 * Live Enhanced Mempool Test
 * Tests the enhanced mempool streamer with real WebSocket connections
 */

import dotenv from 'dotenv';
import EnhancedMempoolStreamer from './src/phoenix/components/enhanced-mempool-streamer.js';

dotenv.config();

async function runLiveTest() {
  console.log('🌐 LIVE ENHANCED MEMPOOL TEST STARTING...\n');
  
  let streamer = null;
  let testTimeout = null;
  
  try {
    // Test configuration
    console.log('⚙️ Test Configuration:');
    console.log(`   • Alchemy WS URL: ${process.env.ALCHEMY_WS_URL ? 'Configured ✅' : 'Missing ❌'}`);
    console.log(`   • QuickNode WS URL: ${process.env.QUICKNODE_WS_URL ? 'Configured ✅' : 'Missing ❌'}`);
    console.log('');
    
    if (!process.env.ALCHEMY_WS_URL) {
      throw new Error('ALCHEMY_WS_URL not configured in .env file');
    }
    
    // Create enhanced mempool streamer with live connections
    console.log('📦 Creating Enhanced Mempool Streamer with live connections...');
    
    streamer = new EnhancedMempoolStreamer({
      symbol: 'ETH',
      enableRealTimeFeeds: true, // Enable live WebSocket connections
      whaleThreshold: 50, // Lower threshold for testing
      logger: {
        info: (key, data) => {
          if (key === 'whale_transaction_detected') {
            console.log(`   🐋 [WHALE] ${JSON.stringify(data, null, 2)}`);
          } else {
            console.log(`   [INFO] ${key}: ${typeof data === 'object' ? JSON.stringify(data) : data}`);
          }
        },
        warn: (key, data) => console.warn(`   [WARN] ${key}: ${typeof data === 'object' ? JSON.stringify(data) : data}`),
        error: (key, data) => console.error(`   [ERROR] ${key}: ${typeof data === 'object' ? JSON.stringify(data) : data}`),
        debug: (key, data) => {
          // Only log important debug messages
          if (key.includes('connection') || key.includes('provider')) {
            console.log(`   [DEBUG] ${key}: ${typeof data === 'object' ? JSON.stringify(data) : data}`);
          }
        }
      }
    });
    
    console.log('   ✅ Enhanced Mempool Streamer created');
    
    // Set up event listeners
    let transactionCount = 0;
    let whaleCount = 0;
    let highValueCount = 0;
    
    streamer.on('WHALE_INTENT_DETECTED', (intent) => {
      whaleCount++;
      console.log(`\n🚨 WHALE INTENT #${whaleCount} DETECTED:`);
      console.log(`   • Address: ${intent.whaleAddress}`);
      console.log(`   • Value: $${intent.estimatedValue.toLocaleString()}`);
      console.log(`   • Threat Level: ${intent.threatLevel}`);
      console.log(`   • Detection Latency: ${intent.detectionLatency}ms`);
      console.log(`   • Provider: ${intent.provider}`);
      console.log(`   • Transaction: ${intent.transactionHash}`);
    });
    
    // Start the streamer
    console.log('\n🚀 Starting enhanced mempool streaming...');
    const started = await streamer.start();
    
    if (!started) {
      throw new Error('Failed to start enhanced mempool streamer');
    }
    
    console.log('   ✅ Enhanced mempool streaming started successfully');
    
    // Monitor for 60 seconds
    console.log('\n⏱️ Monitoring mempool for 60 seconds...');
    console.log('   (Looking for transactions, whale activity, and connection health)');
    
    let lastStatsTime = Date.now();
    const monitoringInterval = setInterval(() => {
      const stats = streamer.getStats();
      const newTransactions = stats.totalTransactions - transactionCount;
      transactionCount = stats.totalTransactions;
      
      if (newTransactions > 0) {
        console.log(`   📊 +${newTransactions} transactions (Total: ${stats.totalTransactions}, Whales: ${stats.whaleTransactions}, Rate: ${stats.transactionsPerSecond}/s)`);
      }
      
      // Check connection health
      if (stats.activeConnections === 0) {
        console.log('   ⚠️ No active connections - checking reconnection status');
      }
    }, 10000); // Every 10 seconds
    
    // Set test timeout
    testTimeout = setTimeout(async () => {
      clearInterval(monitoringInterval);
      
      console.log('\n📊 FINAL TEST RESULTS:');
      const finalStats = streamer.getStats();
      
      console.log('='.repeat(60));
      console.log('LIVE MEMPOOL TEST RESULTS');
      console.log('='.repeat(60));
      console.log(`📈 Total Transactions Processed: ${finalStats.totalTransactions}`);
      console.log(`🐋 Whale Transactions Detected: ${finalStats.whaleTransactions}`);
      console.log(`💰 High-Value Transactions: ${finalStats.highValueTransactions}`);
      console.log(`🔗 Active Connections: ${finalStats.activeConnections}`);
      console.log(`⏱️ Uptime: ${finalStats.uptime}s`);
      console.log(`📊 Transaction Rate: ${finalStats.transactionsPerSecond}/s`);
      console.log(`🎯 Whale Detection Rate: ${finalStats.whaleDetectionRate}`);
      console.log(`🔄 Restart Count: ${finalStats.restartCount}`);
      
      // Assessment
      console.log('\n🎯 ASSESSMENT:');
      
      if (finalStats.totalTransactions > 0) {
        console.log('✅ Transaction Processing: WORKING');
      } else {
        console.log('⚠️ Transaction Processing: NO TRANSACTIONS DETECTED');
        console.log('   (This may be normal during low network activity)');
      }
      
      if (finalStats.activeConnections > 0) {
        console.log('✅ Connection Management: HEALTHY');
      } else {
        console.log('❌ Connection Management: NO ACTIVE CONNECTIONS');
      }
      
      if (finalStats.whaleTransactions > 0) {
        console.log('✅ Whale Detection: FUNCTIONAL');
      } else {
        console.log('⚠️ Whale Detection: NO WHALE ACTIVITY DETECTED');
        console.log('   (This may be normal if no whale transactions occurred)');
      }
      
      // Overall result
      const isWorking = finalStats.activeConnections > 0;
      const hasData = finalStats.totalTransactions > 0;
      
      if (isWorking && hasData) {
        console.log('\n🎉 LIVE TEST RESULT: SUCCESS');
        console.log('🚀 Enhanced mempool streamer is fully operational!');
      } else if (isWorking && !hasData) {
        console.log('\n✅ LIVE TEST RESULT: PARTIAL SUCCESS');
        console.log('🔗 Connections working, waiting for network activity');
      } else {
        console.log('\n❌ LIVE TEST RESULT: CONNECTION ISSUES');
        console.log('🔧 Check WebSocket URLs and network connectivity');
      }
      
      console.log('='.repeat(60));
      
      // Cleanup
      await streamer.stop();
      process.exit(0);
      
    }, 60000); // 60 seconds
    
  } catch (error) {
    console.error('\n💥 LIVE TEST FAILED:', error.message);
    console.error('Stack:', error.stack);
    
    if (testTimeout) {
      clearTimeout(testTimeout);
    }
    
    if (streamer) {
      await streamer.stop();
    }
    
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Test terminated');
  process.exit(0);
});

// Run the live test
runLiveTest().catch(console.error);
