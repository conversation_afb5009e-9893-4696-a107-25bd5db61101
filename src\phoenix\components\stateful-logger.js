/**
 * Phoenix v6.0 - Stateful Logger (Mandate 3)
 *
 * INTELLIGENT STATE-CHANGE-ONLY LOGGING SYSTEM
 *
 * RED TEAM MANDATE: "Eliminate repetitive console spam"
 * PHOENIX SOLUTION: Stateful logging with intelligent noise reduction
 *
 * - Maintains internal cache of last logged values
 * - Only logs when state actually changes
 * - Eliminates "Saved to memory" and repetitive momentum spam
 * - Provides structured JSON logging with contextual metadata
 * - File-based persistence with rotation
 */

import fs from 'fs';
import path from 'path';
import { getISTTime } from '../../utils/index.js';

export default class StatefulLogger {
  constructor(config = {}) {
    this.config = {
      enableFileLogging: config.enableFileLogging || false,
      enableConsoleLogging: config.enableConsoleLogging !== false,
      logDirectory: config.logDirectory || './logs/phoenix',
      stateChangeOnly: config.stateChangeOnly !== false,
      minLogLevel: config.minLogLevel || 1, // INFO
      // RED TEAM MANDATE 4: Performance optimizations
      conciseMode: config.conciseMode !== false, // Enable concise logging by default
      maxObjectDepth: config.maxObjectDepth || 2, // Limit JSON depth
      batchSize: config.batchSize || 100, // Batch writes for performance
      ...config
    };

    // State cache for change detection
    this.stateCache = new Map();

    // RED TEAM MANDATE 4: High-frequency event categories for concise logging
    this.highFrequencyEvents = new Set([
      'connection_status_check',
      'engine_heartbeat',
      'task_completed',
      'derivatives_update',
      'classifier_ETHUSDT',
      'whale_balance_check',
      'health_check'
    ]);

    // Log levels
    this.levels = {
      DEBUG: 0,
      INFO: 1,
      WARN: 2,
      ERROR: 3,
      CRITICAL: 4
    };

    // Performance stats
    this.stats = {
      totalLogs: 0,
      stateLogs: 0,
      duplicatesFiltered: 0,
      fileWrites: 0,
      consoleWrites: 0,
      conciseLogs: 0,
      verboseLogs: 0,
      startTime: Date.now()
    };

    // RED TEAM MANDATE 4: Batch writing for performance
    this.logBatch = [];
    this.batchTimer = null;

    // Setup file logging if enabled
    if (this.config.enableFileLogging) {
      this.setupFileLogging();
    }

    console.log('📝 Stateful Logger v6.1 initialized (Performance Optimized)');
  }

  /**
   * MANDATE 3: Main logging method with intelligent state change detection
   *
   * Eliminates repetitive console spam ("Saved to memory", momentum calculations)
   * by maintaining internal cache and only logging when state actually changes.
   */
  log(key, value, level = this.levels.INFO, metadata = {}) {
    this.stats.totalLogs++;
    
    // Create log entry
    const entry = this.createLogEntry(key, value, level, metadata);
    
    // MANDATE 3: Check for state change (intelligent noise reduction)
    if (this.config.stateChangeOnly && this.isDuplicate(entry)) {
      this.stats.duplicatesFiltered++;

      // MANDATE 3: Successfully eliminated repetitive logging
      this.stats.spamPrevented = (this.stats.spamPrevented || 0) + 1;
      return false;
    }
    
    // Update state cache
    this.updateStateCache(entry);
    this.stats.stateLogs++;

    // Track mandate compliance
    this.stats.mandateCompliance = (this.stats.mandateCompliance || 0) + 1;
    
    // Apply minimum log level filter
    if (entry.level < this.config.minLogLevel) {
      return false;
    }
    
    // Output to console
    if (this.config.enableConsoleLogging) {
      this.writeToConsole(entry);
    }
    
    // Output to file
    if (this.config.enableFileLogging && this.logFileStream) {
      this.writeToFile(entry);
    }
    
    return true;
  }

  /**
   * Create log entry object
   */
  createLogEntry(key, value, level, metadata) {
    const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
    
    return {
      key,
      value,
      level,
      metadata,
      timestamp: Date.now(),
      istTime: getISTTime(),
      hash: this.calculateHash(key + valueStr)
    };
  }

  /**
   * Calculate hash for change detection
   */
  calculateHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash;
  }

  /**
   * Check if log entry is duplicate
   */
  isDuplicate(entry) {
    const cachedEntry = this.stateCache.get(entry.key);
    if (!cachedEntry) {
      return false; // First time logging this key
    }
    
    return cachedEntry.hash === entry.hash;
  }

  /**
   * Update state cache
   */
  updateStateCache(entry) {
    this.stateCache.set(entry.key, {
      hash: entry.hash,
      value: entry.value,
      timestamp: entry.timestamp,
      count: (this.stateCache.get(entry.key)?.count || 0) + 1
    });
  }

  /**
   * RED TEAM MANDATE 4: Optimized console writing with concise mode
   */
  writeToConsole(entry) {
    const levelStr = Object.keys(this.levels)[entry.level] || 'INFO';

    // RED TEAM MANDATE 4: Use concise format for high-frequency events
    let formatted;
    if (this.config.conciseMode && this.highFrequencyEvents.has(entry.key)) {
      // Concise single-line format for high-frequency events
      const valueStr = this.formatValueConcise(entry.value);
      formatted = `[${levelStr}] ${entry.key}: ${valueStr}`;
      this.stats.conciseLogs++;
    } else {
      // Verbose format for important events
      const valueStr = typeof entry.value === 'object' ?
        JSON.stringify(entry.value, null, 2) : entry.value;
      formatted = `[${entry.istTime}] [${levelStr}] ${entry.key}: ${valueStr}`;
      this.stats.verboseLogs++;
    }

    switch (entry.level) {
      case this.levels.DEBUG:
        console.debug(formatted);
        break;
      case this.levels.INFO:
        console.log(formatted);
        break;
      case this.levels.WARN:
        console.warn(formatted);
        break;
      case this.levels.ERROR:
      case this.levels.CRITICAL:
        console.error(formatted);
        break;
      default:
        console.log(formatted);
    }

    this.stats.consoleWrites++;
  }

  /**
   * RED TEAM MANDATE 4: Format values concisely for high-frequency events
   */
  formatValueConcise(value) {
    if (typeof value !== 'object' || value === null) {
      return String(value);
    }

    // Extract key metrics for concise display
    if (value.logType === 'HEARTBEAT') {
      return `Status: ${value.status}, Health: ${value.healthSummary}, Uptime: ${value.uptime}s`;
    }

    if (value.logType === 'DIAGNOSTIC') {
      const regime = value.classifierOutput?.regime || 'NO_REGIME';
      const scores = value.classifierOutput?.weightedScoring?.scores;
      if (scores) {
        return `Regime: ${regime}, Scores: C:${scores.CASCADE}% O:${scores.COIL}% S:${scores.SHAKEOUT}%`;
      }
      return `Regime: ${regime}`;
    }

    if (value.activeConnections !== undefined) {
      return `Connections: ${value.activeConnections}, Transactions: ${value.totalTransactions}, Health: ${value.connectionHealth}`;
    }

    if (value.taskId) {
      return `Task: ${value.taskId}, Time: ${value.executionTime}ms`;
    }

    // Fallback: truncated JSON
    const jsonStr = JSON.stringify(value);
    return jsonStr.length > 100 ? jsonStr.substring(0, 97) + '...' : jsonStr;
  }

  /**
   * RED TEAM MANDATE 4: Optimized file writing with batching
   */
  writeToFile(entry) {
    if (!this.logFileStream) return;

    const levelStr = Object.keys(this.levels)[entry.level] || 'INFO';
    const valueStr = typeof entry.value === 'object' ?
      JSON.stringify(entry.value) : entry.value;

    const formatted = `[${entry.istTime}] [${levelStr}] ${entry.key}: ${valueStr}\n`;

    // RED TEAM MANDATE 4: Add to batch instead of immediate write
    this.logBatch.push(formatted);

    // Flush batch when it reaches the configured size or on critical/error logs
    if (this.logBatch.length >= this.config.batchSize || entry.level >= this.levels.ERROR) {
      this.flushLogBatch();
    } else {
      // Set timer to flush batch periodically
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => {
          this.flushLogBatch();
        }, 1000); // Flush every second
      }
    }
  }

  /**
   * RED TEAM MANDATE 4: Flush log batch to file
   */
  flushLogBatch() {
    if (this.logBatch.length === 0) return;

    try {
      const batchContent = this.logBatch.join('');
      this.logFileStream.write(batchContent);
      this.stats.fileWrites += this.logBatch.length;
      this.logBatch = [];

      // Clear timer
      if (this.batchTimer) {
        clearTimeout(this.batchTimer);
        this.batchTimer = null;
      }
    } catch (error) {
      console.error('Failed to write log batch to file:', error.message);
      this.logBatch = []; // Clear batch to prevent memory leak
    }
  }

  /**
   * Setup file logging
   */
  setupFileLogging() {
    try {
      // Create log directory
      if (!fs.existsSync(this.config.logDirectory)) {
        fs.mkdirSync(this.config.logDirectory, { recursive: true });
      }
      
      // Create log file
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const logFileName = `phoenix-v6-${timestamp}.log`;
      const logFilePath = path.join(this.config.logDirectory, logFileName);
      
      // Create write stream
      this.logFileStream = fs.createWriteStream(logFilePath, { flags: 'a' });
      this.currentLogFile = logFilePath;
      
      // Write header
      this.logFileStream.write(`=== Phoenix Engine v6.0 Log Started at ${getISTTime()} ===\n`);
      
      console.log(`📁 File logging enabled: ${logFilePath}`);
      
    } catch (error) {
      console.error('Failed to setup file logging:', error.message);
      this.config.enableFileLogging = false;
    }
  }

  /**
   * Convenience methods for different log levels
   */
  debug(key, value, metadata = {}) {
    return this.log(key, value, this.levels.DEBUG, metadata);
  }

  info(key, value, metadata = {}) {
    return this.log(key, value, this.levels.INFO, metadata);
  }

  warn(key, value, metadata = {}) {
    return this.log(key, value, this.levels.WARN, metadata);
  }

  error(key, value, metadata = {}) {
    return this.log(key, value, this.levels.ERROR, metadata);
  }

  critical(key, value, metadata = {}) {
    return this.log(key, value, this.levels.CRITICAL, metadata);
  }

  /**
   * Force log regardless of state change
   */
  force(key, value, level = this.levels.INFO, metadata = {}) {
    const originalSetting = this.config.stateChangeOnly;
    this.config.stateChangeOnly = false;
    
    const result = this.log(key, value, level, metadata);
    
    this.config.stateChangeOnly = originalSetting;
    return result;
  }

  /**
   * Get current state cache
   */
  getStateCache() {
    const cache = {};
    for (const [key, value] of this.stateCache) {
      cache[key] = {
        value: value.value,
        timestamp: value.timestamp,
        count: value.count
      };
    }
    return cache;
  }

  /**
   * Clear state cache
   */
  clearStateCache() {
    const size = this.stateCache.size;
    this.stateCache.clear();
    console.log(`📝 Cleared state cache (${size} entries)`);
  }

  /**
   * Get performance statistics
   */
  getStats() {
    const uptime = Date.now() - this.stats.startTime;
    const filterEfficiency = this.stats.totalLogs > 0 ? 
      (this.stats.duplicatesFiltered / this.stats.totalLogs * 100) : 0;
    
    return {
      ...this.stats,
      uptime: Math.floor(uptime / 1000),
      cacheSize: this.stateCache.size,
      filterEfficiency: Math.round(filterEfficiency * 100) / 100,
      avgLogsPerSecond: Math.round((this.stats.totalLogs / (uptime / 1000)) * 100) / 100
    };
  }

  /**
   * RED TEAM MANDATE 4: Enhanced shutdown with batch flush and performance stats
   */
  shutdown() {
    console.log('📝 Shutting down Stateful Logger...');

    // RED TEAM MANDATE 4: Flush any remaining logs in batch
    if (this.logBatch.length > 0) {
      this.flushLogBatch();
    }

    // Clear any pending timer
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    if (this.logFileStream) {
      this.logFileStream.write(`=== Phoenix Engine v6.1 Log Ended at ${getISTTime()} ===\n`);
      this.logFileStream.end();
    }

    const stats = this.getStats();
    // RED TEAM MANDATE 4: Enhanced performance reporting
    const performanceGain = stats.totalLogs > 0 ?
      `${Math.round((this.stats.conciseLogs / stats.totalLogs) * 100)}% concise` : 'N/A';

    console.log(`📊 Final stats: ${stats.totalLogs} total, ${stats.duplicatesFiltered} filtered, ${stats.filterEfficiency}% efficiency, Performance: ${performanceGain}`);
  }
}
