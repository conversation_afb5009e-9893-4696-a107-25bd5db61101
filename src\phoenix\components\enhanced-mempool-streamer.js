/**
 * Enhanced Mempool Streamer v6.2
 * 
 * Modern implementation using Ethers.js and Viem for optimal mempool monitoring
 * Based on QuickNode best practices and latest Web3 standards
 */

import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import { createPublicClient, webSocket } from 'viem';
import { mainnet } from 'viem/chains';

export default class EnhancedMempoolStreamer extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = {
      symbol: config.symbol || 'ETH',
      logger: config.logger,
      enableRealTimeFeeds: config.enableRealTimeFeeds !== false,
      providers: config.providers || {},
      whaleThreshold: config.whaleThreshold || 100, // ETH
      ...config
    };
    
    // Provider connections
    this.ethersProvider = null;
    this.viemClient = null;
    this.activeConnections = new Map();
    
    // Whale monitoring
    this.whaleWatchlist = new Set(process.env.WHALE_WATCHLIST?.split(',') || []);
    
    // Performance tracking
    this.stats = {
      totalTransactions: 0,
      whaleTransactions: 0,
      highValueTransactions: 0,
      connectionUptime: 0,
      lastTransactionTime: null,
      startTime: Date.now(),
      restartCount: 0,
      providerSwitches: 0
    };
    
    // Connection monitoring
    this.isStreaming = false;
    this.connectionMonitorInterval = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    
    this.logger?.info('enhanced_mempool_streamer_init', {
      whaleWatchlistSize: this.whaleWatchlist.size,
      enableRealTimeFeeds: this.config.enableRealTimeFeeds,
      providers: Object.keys(this.config.providers)
    });
  }

  /**
   * Start enhanced mempool streaming with multiple providers
   */
  async start() {
    if (!this.config.enableRealTimeFeeds) {
      this.logger?.warn('mempool_streaming_disabled', 'Real-time feeds disabled by configuration');
      return true;
    }

    this.logger?.info('enhanced_mempool_start', 'Starting enhanced mempool streaming');

    try {
      // Initialize primary provider (Ethers.js)
      await this.initializeEthersProvider();
      
      // Initialize secondary provider (Viem) for redundancy
      await this.initializeViemProvider();
      
      // Start connection monitoring
      this.startConnectionMonitoring();
      
      this.isStreaming = true;
      this.stats.startTime = Date.now();
      
      this.logger?.info('enhanced_mempool_operational', {
        activeProviders: this.activeConnections.size,
        whaleWatchlistSize: this.whaleWatchlist.size
      });
      
      return true;
      
    } catch (error) {
      this.logger?.error('enhanced_mempool_start_failed', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Initialize Ethers.js provider (Primary)
   */
  async initializeEthersProvider() {
    const wsUrl = process.env.ALCHEMY_WS_URL || process.env.QUICKNODE_WS_URL;
    
    if (!wsUrl) {
      throw new Error('No WebSocket URL configured for Ethers provider');
    }

    try {
      this.ethersProvider = new ethers.WebSocketProvider(wsUrl);
      
      // Set up event listeners
      this.ethersProvider.on("pending", async (txHash) => {
        await this.processEthersTransaction(txHash);
      });

      this.ethersProvider.on("error", (error) => {
        this.logger?.error('ethers_provider_error', {
          error: error.message,
          action: 'ATTEMPTING_RECONNECTION'
        });
        this.handleProviderError('ethers', error);
      });

      // Test connection
      await this.ethersProvider.getNetwork();
      
      this.activeConnections.set('ethers', {
        provider: this.ethersProvider,
        type: 'primary',
        status: 'connected',
        lastActivity: Date.now()
      });
      
      this.logger?.info('ethers_provider_connected', 'Primary mempool provider active');
      
    } catch (error) {
      this.logger?.error('ethers_provider_init_failed', {
        error: error.message,
        wsUrl: wsUrl.substring(0, 50) + '...'
      });
      throw error;
    }
  }

  /**
   * Initialize Viem provider (Secondary/Backup)
   */
  async initializeViemProvider() {
    const wsUrl = process.env.QUICKNODE_WS_URL || process.env.ALCHEMY_WS_URL;
    
    if (!wsUrl) {
      this.logger?.warn('viem_provider_skip', 'No backup WebSocket URL configured');
      return;
    }

    try {
      this.viemClient = createPublicClient({
        chain: mainnet,
        transport: webSocket(wsUrl)
      });

      // Start watching pending transactions
      const unwatch = this.viemClient.watchPendingTransactions({
        onTransactions: async (hashes) => {
          for (const hash of hashes) {
            await this.processViemTransaction(hash);
          }
        },
        onError: (error) => {
          this.logger?.error('viem_provider_error', {
            error: error.message,
            action: 'ATTEMPTING_RECONNECTION'
          });
          this.handleProviderError('viem', error);
        }
      });

      this.activeConnections.set('viem', {
        client: this.viemClient,
        unwatch,
        type: 'backup',
        status: 'connected',
        lastActivity: Date.now()
      });
      
      this.logger?.info('viem_provider_connected', 'Backup mempool provider active');
      
    } catch (error) {
      this.logger?.warn('viem_provider_init_failed', {
        error: error.message,
        note: 'Continuing with primary provider only'
      });
    }
  }

  /**
   * Process transaction from Ethers.js provider
   */
  async processEthersTransaction(txHash) {
    try {
      const tx = await this.ethersProvider.getTransaction(txHash);
      if (!tx) return;

      this.stats.totalTransactions++;
      this.stats.lastTransactionTime = Date.now();
      
      // Update connection activity
      const connection = this.activeConnections.get('ethers');
      if (connection) {
        connection.lastActivity = Date.now();
      }

      await this.analyzeTransaction({
        hash: tx.hash,
        from: tx.from,
        to: tx.to,
        value: tx.value ? ethers.formatEther(tx.value) : '0',
        gasPrice: tx.gasPrice ? ethers.formatUnits(tx.gasPrice, 'gwei') : '0',
        gasLimit: tx.gasLimit ? tx.gasLimit.toString() : '0',
        provider: 'ethers',
        timestamp: Date.now()
      });

    } catch (error) {
      this.logger?.debug('ethers_tx_process_error', {
        txHash,
        error: error.message
      });
    }
  }

  /**
   * Process transaction from Viem provider
   */
  async processViemTransaction(txHash) {
    try {
      const tx = await this.viemClient.getTransaction({ hash: txHash });
      if (!tx) return;

      this.stats.totalTransactions++;
      this.stats.lastTransactionTime = Date.now();
      
      // Update connection activity
      const connection = this.activeConnections.get('viem');
      if (connection) {
        connection.lastActivity = Date.now();
      }

      await this.analyzeTransaction({
        hash: tx.hash,
        from: tx.from,
        to: tx.to,
        value: tx.value ? (Number(tx.value) / 1e18).toString() : '0',
        gasPrice: tx.gasPrice ? (Number(tx.gasPrice) / 1e9).toString() : '0',
        gasLimit: tx.gas ? tx.gas.toString() : '0',
        provider: 'viem',
        timestamp: Date.now()
      });

    } catch (error) {
      this.logger?.debug('viem_tx_process_error', {
        txHash,
        error: error.message
      });
    }
  }

  /**
   * Analyze transaction for whale activity and high-value transfers
   */
  async analyzeTransaction(txData) {
    const { hash, from, to, value, provider } = txData;
    const valueETH = parseFloat(value);
    const valueUSD = valueETH * 3500; // Approximate ETH price

    // Check for whale addresses
    const isWhaleFrom = this.whaleWatchlist.has(from?.toLowerCase());
    const isWhaleTo = this.whaleWatchlist.has(to?.toLowerCase());
    const isHighValue = valueETH >= this.config.whaleThreshold;

    if (isWhaleFrom || isWhaleTo || isHighValue) {
      this.stats.whaleTransactions++;
      
      const whaleData = {
        hash,
        from,
        to,
        valueETH: Math.round(valueETH * 100) / 100,
        valueUSD: Math.round(valueUSD),
        isWhaleFrom,
        isWhaleTo,
        isHighValue,
        provider,
        timestamp: txData.timestamp,
        detectionLatency: Date.now() - txData.timestamp
      };

      // Log whale transaction
      this.logger?.info('whale_transaction_detected', whaleData);

      // Emit whale intent event
      this.emit('WHALE_INTENT_DETECTED', {
        whaleAddress: isWhaleFrom ? from : to,
        estimatedValue: valueUSD,
        threatLevel: valueUSD > 1000000 ? 'CRITICAL' : valueUSD > 100000 ? 'HIGH' : 'MEDIUM',
        detectionLatency: whaleData.detectionLatency,
        transactionHash: hash,
        provider
      });
    }

    // Track high-value transactions
    if (valueUSD > 50000) {
      this.stats.highValueTransactions++;
    }
  }

  /**
   * Handle provider errors and attempt recovery
   */
  async handleProviderError(providerName, error) {
    this.logger?.error('provider_error_detected', {
      provider: providerName,
      error: error.message,
      reconnectAttempts: this.reconnectAttempts
    });

    const connection = this.activeConnections.get(providerName);
    if (connection) {
      connection.status = 'error';
    }

    // Attempt reconnection if under limit
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        this.attemptReconnection(providerName);
      }, 5000 * this.reconnectAttempts); // Exponential backoff
    } else {
      this.logger?.error('provider_max_reconnects_exceeded', {
        provider: providerName,
        maxAttempts: this.maxReconnectAttempts
      });
    }
  }

  /**
   * Attempt to reconnect a specific provider
   */
  async attemptReconnection(providerName) {
    this.logger?.info('provider_reconnection_attempt', {
      provider: providerName,
      attempt: this.reconnectAttempts
    });

    try {
      if (providerName === 'ethers') {
        await this.initializeEthersProvider();
      } else if (providerName === 'viem') {
        await this.initializeViemProvider();
      }
      
      this.reconnectAttempts = 0; // Reset on successful reconnection
      this.stats.restartCount++;
      
    } catch (error) {
      this.logger?.error('provider_reconnection_failed', {
        provider: providerName,
        error: error.message
      });
    }
  }

  /**
   * Start connection monitoring
   */
  startConnectionMonitoring() {
    this.connectionMonitorInterval = setInterval(() => {
      this.monitorConnections();
    }, 30000); // Every 30 seconds
  }

  /**
   * Monitor connection health
   */
  monitorConnections() {
    const now = Date.now();
    let healthyConnections = 0;
    
    for (const [providerName, connection] of this.activeConnections) {
      const timeSinceActivity = now - connection.lastActivity;
      
      if (timeSinceActivity > 120000) { // 2 minutes without activity
        this.logger?.warn('provider_stalled', {
          provider: providerName,
          stalledDuration: Math.round(timeSinceActivity / 1000),
          action: 'TRIGGERING_RECONNECTION'
        });
        
        this.handleProviderError(providerName, new Error('Connection stalled'));
      } else {
        healthyConnections++;
      }
    }

    // Log connection status
    this.logger?.debug('connection_health_check', {
      totalConnections: this.activeConnections.size,
      healthyConnections,
      totalTransactions: this.stats.totalTransactions,
      whaleTransactions: this.stats.whaleTransactions
    });
  }

  /**
   * Get streaming statistics
   */
  getStats() {
    const uptime = Date.now() - this.stats.startTime;
    
    return {
      ...this.stats,
      uptime: Math.floor(uptime / 1000),
      activeConnections: this.activeConnections.size,
      transactionsPerSecond: this.stats.totalTransactions > 0 ? 
        (this.stats.totalTransactions / (uptime / 1000)).toFixed(2) : 0,
      whaleDetectionRate: this.stats.totalTransactions > 0 ? 
        ((this.stats.whaleTransactions / this.stats.totalTransactions) * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * Stop mempool streaming
   */
  async stop() {
    this.logger?.info('enhanced_mempool_stop', 'Stopping enhanced mempool streaming');

    // Clear monitoring interval
    if (this.connectionMonitorInterval) {
      clearInterval(this.connectionMonitorInterval);
      this.connectionMonitorInterval = null;
    }

    // Close all connections
    for (const [providerName, connection] of this.activeConnections) {
      try {
        if (connection.unwatch) {
          connection.unwatch(); // Viem unwatch function
        }
        if (connection.provider && connection.provider.destroy) {
          connection.provider.destroy(); // Ethers provider cleanup
        }
        
        this.logger?.info('provider_disconnected', providerName);
      } catch (error) {
        this.logger?.warn('provider_disconnect_error', {
          provider: providerName,
          error: error.message
        });
      }
    }

    this.activeConnections.clear();
    this.isStreaming = false;
    
    this.logger?.info('enhanced_mempool_stopped', {
      finalStats: this.getStats()
    });
  }
}
