/**
 * SentryCoin v6.0 - Phoenix Engine (Clean Implementation)
 * 
 * PROJECT PHOENIX - COMPLETE SYSTEM REORGANIZATION
 * 
 * This is the main Phoenix Engine with all 5 Red Team mandates
 * properly integrated and connected.
 */

import { EventEmitter } from 'events';
import LiquidityAnalyzer from './components/liquidity-analyzer.js';
import MempoolStreamer from './components/mempool-streamer.js';
import DerivativesMonitor from './components/derivatives-monitor.js';
import TaskScheduler from './components/task-scheduler.js';
import StatefulLogger from './components/stateful-logger.js';
import TelegramReporter from './components/telegram-reporter.js';
import MarketClassifier from './components/market-classifier.js';
import ResilientAPIClient from './components/resilient-api-client.js';
import { getISTTime } from '../utils/index.js';

export default class PhoenixEngine extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.version = '6.0.0';
    this.config = {
      symbol: config.symbol || 'ETHUSDT',
      paperTrading: config.paperTrading !== false,
      enableRealTimeFeeds: config.enableRealTimeFeeds !== false,
      ...config
    };
    
    // Initialize stateful logger first (Mandate 3)
    this.logger = new StatefulLogger({
      enableFileLogging: true,
      logDirectory: './logs/phoenix-v6',
      stateChangeOnly: true
    });
    
    // Core Phoenix components
    this.liquidityAnalyzer = null;    // Mandate 1
    this.mempoolStreamer = null;      // Mandate 2
    this.marketClassifier = null;     // Mandate 2 (Observability)
    this.derivativesMonitor = null;   // Mandate 4
    this.taskScheduler = null;        // Mandate 5
    this.telegramReporter = null;     // Notifications
    this.apiClient = null;            // Mandate 3 (Resilience)
    
    // System state
    this.isRunning = false;
    this.startTime = null;
    // CRUCIBLE MANDATE 4: Realistic status reporting
    this.systemHealth = {
      liquidityAnalyzer: 'INITIALIZING',
      mempoolStreamer: 'INITIALIZING',
      marketClassifier: 'INITIALIZING',
      derivativesMonitor: 'INITIALIZING',
      taskScheduler: 'INITIALIZING',
      telegramReporter: 'INITIALIZING'
    };

    // CRUCIBLE MANDATE 4: Component activity tracking for STALLED detection
    this.lastActivity = {
      liquidityAnalyzer: Date.now(),
      mempoolStreamer: Date.now(),
      marketClassifier: Date.now(),
      derivativesMonitor: Date.now(),
      taskScheduler: Date.now(),
      telegramReporter: Date.now()
    };
    
    // Performance metrics
    this.metrics = {
      whaleIntentsDetected: 0,
      liquidityValidations: 0,
      derivativesUpdates: 0,
      tasksExecuted: 0,
      alertsSent: 0,
      startTime: Date.now()
    };

    // RED TEAM MANDATE 5: Sanity check system
    this.sanityCheck = {
      enabled: true,
      classificationThreshold: 100, // Halt after 100 classifications with zero regimes
      totalClassifications: 0,
      totalRegimeDetections: 0,
      lastRegimeDetection: null,
      alertsSent: 0,
      emergencyShutdownTriggered: false
    };
    
    this.logger.info('phoenix_engine_init', {
      version: this.version,
      symbol: this.config.symbol,
      paperTrading: this.config.paperTrading
    });
  }

  /**
   * Initialize all Phoenix components
   */
  async initialize() {
    this.logger.info('phoenix_initialization_start', 'Initializing all Phoenix components');
    
    try {
      // Initialize Telegram Reporter first for notifications
      this.telegramReporter = new TelegramReporter({
        botToken: process.env.TELEGRAM_BOT_TOKEN,
        chatId: process.env.TELEGRAM_CHAT_ID
      });
      await this.telegramReporter.initialize();
      this.systemHealth.telegramReporter = 'ONLINE';
      this.logger.info('telegram_reporter_ready', 'Telegram notifications active');
      
      // Initialize Liquidity Analyzer (Mandate 1)
      this.liquidityAnalyzer = new LiquidityAnalyzer({
        symbol: this.config.symbol,
        logger: this.logger
      });
      this.systemHealth.liquidityAnalyzer = 'ONLINE';
      this.logger.info('mandate_1_ready', 'Dynamic Liquidity Analyzer operational');
      
      // Initialize Mempool Streamer (Mandate 2)
      // Primary: Alchemy, Backup: QuickNode (Blocknative deprecated March 2025)
      const mempoolProviders = {
        alchemy: { enabled: !!process.env.ALCHEMY_API_KEY },
        quicknode: { enabled: !!process.env.QUICKNODE_WS_URL }
      };

      // Check if any mempool providers are available
      const hasProviders = Object.values(mempoolProviders).some(p => p.enabled);

      this.mempoolStreamer = new MempoolStreamer({
        symbol: this.config.symbol.replace('USDT', ''),
        logger: this.logger,
        providers: mempoolProviders,
        enableRealTimeFeeds: hasProviders && this.config.enableRealTimeFeeds
      });

      this.systemHealth.mempoolStreamer = hasProviders ? 'ONLINE' : 'LIMITED';

      if (hasProviders) {
        this.logger.info('mandate_2_ready', 'Event-driven mempool streaming operational');
      } else {
        this.logger.warn('mandate_2_limited', 'Mempool streaming disabled - no API providers configured');
      }

      // Initialize Market Classifier (Mandate 2 - Observability)
      this.marketClassifier = new MarketClassifier({
        symbol: this.config.symbol,
        logger: this.logger
      });
      this.systemHealth.marketClassifier = 'ONLINE';
      this.logger.info('mandate_2_observability_ready', 'Market classifier with diagnostic logging operational');
      
      // Initialize Derivatives Monitor (Mandate 4)
      this.derivativesMonitor = new DerivativesMonitor({
        symbol: this.config.symbol,
        logger: this.logger,
        bybitConfig: {
          apiKey: process.env.BYBIT_API_KEY,
          apiSecret: process.env.BYBIT_API_SECRET
        }
      });
      this.systemHealth.derivativesMonitor = 'ONLINE';
      this.logger.info('mandate_4_ready', 'Real-time derivatives feed operational');
      
      // Initialize Task Scheduler (Mandate 5)
      this.taskScheduler = new TaskScheduler({
        maxConcurrentTasks: 8,
        logger: this.logger
      });
      await this.taskScheduler.initialize();
      this.systemHealth.taskScheduler = 'ONLINE';
      this.logger.info('mandate_5_ready', 'Microservice task scheduler operational');

      // Initialize Resilient API Client (Mandate 3)
      this.apiClient = new ResilientAPIClient({
        logger: this.logger,
        providers: {
          binance: {
            name: 'binance',
            enabled: true,
            baseUrl: 'https://api.binance.com',
            headers: {},
            timeout: 10000,
            priority: 1
          },
          binanceBackup: {
            name: 'binance-backup',
            enabled: true,
            baseUrl: 'https://api1.binance.com',
            headers: {},
            timeout: 10000,
            priority: 2
          }
        }
      });
      this.logger.info('mandate_3_ready', 'Resilient API client with circuit breaker operational');

      // Setup component event handlers
      this.setupEventHandlers();
      
      this.logger.info('phoenix_initialization_complete', {
        systemHealth: this.systemHealth,
        mandatesImplemented: 5
      });
      
      return true;
      
    } catch (error) {
      this.logger.error('phoenix_initialization_failed', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Setup event handlers between components
   */
  setupEventHandlers() {
    // Whale intent detection from mempool streamer
    this.mempoolStreamer.on('WHALE_INTENT_DETECTED', async (intent) => {
      this.metrics.whaleIntentsDetected++;

      // CRUCIBLE MANDATE 4: Track component activity
      this.updateComponentActivity('mempoolStreamer', true, false);

      // RED TEAM MANDATE 3: Integrate whale transactions into Market Classifier
      if (this.marketClassifier && intent.estimatedValue >= 10000) {
        this.marketClassifier.processWhaleTransaction({
          type: 'WHALE_TRANSACTION',
          data: {
            value: intent.estimatedValue,
            address: intent.whaleAddress,
            threatLevel: intent.threatLevel
          },
          timestamp: Date.now()
        });

        this.logger.info('whale_transaction_integrated', {
          whaleAddress: intent.whaleAddress,
          estimatedValue: intent.estimatedValue,
          message: 'High-value whale transaction sent to Market Classifier for threshold adjustment'
        });
      }

      this.logger.warn('whale_intent_detected', {
        whaleAddress: intent.whaleAddress,
        estimatedValue: intent.estimatedValue,
        threatLevel: intent.threatLevel,
        detectionLatency: intent.detectionLatency
      });
      
      // Send immediate Telegram alert for critical whales
      if (intent.threatLevel === 'CRITICAL') {
        await this.telegramReporter.sendAlert({
          type: 'CRITICAL_WHALE_ALERT',
          title: '🚨 CRITICAL WHALE INTENT DETECTED',
          message: `Whale: ${intent.whaleAddress}\nValue: $${intent.estimatedValue.toLocaleString()}\nExchange: ${intent.targetExchange}\nLatency: ${intent.detectionLatency}ms`,
          priority: 'HIGH'
        });
        this.metrics.alertsSent++;
      }
      
      // Emit system-wide event
      this.emit('WHALE_INTENT', intent);
    });
    
    // Derivatives updates from monitor
    this.derivativesMonitor.on('DERIVATIVES_UPDATE', (update) => {
      this.metrics.derivativesUpdates++;

      // CRUCIBLE MANDATE 4: Track component activity
      this.updateComponentActivity('derivativesMonitor', true, false);

      // RED TEAM MANDATE 3: Integrate derivatives alerts into Market Classifier
      if (this.marketClassifier && update.type === 'OI_SPIKE') {
        this.marketClassifier.processDerivativesAlert(update.data);
        this.logger.info('derivatives_alert_integrated', {
          alertType: update.type,
          exchange: update.exchange,
          message: 'OI_SPIKE alert sent to Market Classifier for threshold adjustment'
        });
      }

      this.logger.debug('derivatives_update', {
        type: update.type,
        exchange: update.exchange
      });

      // Check for significant derivatives events
      if (update.type === 'OI_SPIKE' || update.type === 'FUNDING_SPIKE') {
        this.emit('DERIVATIVES_ALERT', update);
      }
    });
    
    // Task completion from scheduler
    this.taskScheduler.on('TASK_COMPLETED', (task) => {
      this.metrics.tasksExecuted++;

      // CRUCIBLE MANDATE 4: Track component activity
      this.updateComponentActivity('taskScheduler', true, false);

      this.logger.debug('task_completed', {
        taskId: task.id,
        type: task.type,
        executionTime: task.executionTime
      });
    });
    
    // Liquidity analysis results
    this.liquidityAnalyzer.on('LIQUIDITY_ANALYSIS', (analysis) => {
      this.metrics.liquidityValidations++;

      // CRUCIBLE MANDATE 4: Track component activity
      this.updateComponentActivity('liquidityAnalyzer', true, false);

      if (analysis.regime === 'CRITICAL') {
        this.logger.warn('critical_liquidity_detected', analysis);
      }
    });
  }

  /**
   * Start the Phoenix Engine
   */
  async start() {
    if (this.isRunning) {
      this.logger.warn('phoenix_already_running', 'Phoenix Engine already running');
      return true;
    }
    
    this.logger.info('phoenix_start', 'Starting Phoenix Engine v6.0');
    
    const initialized = await this.initialize();
    if (!initialized) {
      return false;
    }
    
    try {
      // Start all components
      if (this.config.enableRealTimeFeeds) {
        const mempoolStarted = await this.mempoolStreamer.start();
        const derivativesStarted = await this.derivativesMonitor.start();

        if (!mempoolStarted) {
          this.logger.warn('mempool_start_failed', 'Mempool streaming failed to start');
        }
        if (!derivativesStarted) {
          this.logger.warn('derivatives_start_failed', 'Derivatives monitoring failed to start');
        }
      } else {
        this.logger.info('realtime_feeds_disabled', 'Real-time feeds disabled by configuration');
      }

      await this.taskScheduler.start();
      
      // Schedule periodic tasks
      this.schedulePeriodicTasks();
      
      this.isRunning = true;
      this.startTime = Date.now();
      this.metrics.startTime = Date.now();
      
      // Send startup notification
      await this.telegramReporter.sendAlert({
        type: 'SYSTEM_STARTUP',
        title: 'SentryCoin v6.0 Operational',
        message: `System Status: ALL MANDATES ACTIVE\nSymbol: ${this.config.symbol}\nMode: ${this.config.paperTrading ? 'PAPER TRADING' : 'LIVE TRADING'}\nTime: ${getISTTime()}`,
        priority: 'NORMAL'
      });
      
      this.logger.info('phoenix_operational', {
        version: this.version,
        systemHealth: this.systemHealth,
        startTime: getISTTime()
      });
      
      // RED TEAM MANDATE 4: Professional status reporting only
      console.log('\nSentryCoin v6.0 Engine Started');
      console.log('📊 System Status: Components initialized');
      console.log('⚙️ Mode: ' + (this.config.paperTrading ? 'Paper Trading' : 'Live Trading'));
      console.log('📡 Real-time Feeds: ' + (this.config.enableRealTimeFeeds ? 'Enabled' : 'Disabled') + '\n');
      
      return true;
      
    } catch (error) {
      this.logger.error('phoenix_start_failed', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Schedule periodic maintenance tasks
   */
  schedulePeriodicTasks() {
    // CRUCIBLE MANDATE 2: Market classification every 30 seconds to feed the Glass Box
    setInterval(() => {
      this.performMarketClassification();
    }, 30000);

    // FORTRESS v6.1: Engine heartbeat every 60 seconds
    setInterval(() => {
      this.emitHeartbeat();
    }, 60000);

    // System health check every 30 seconds
    setInterval(() => {
      this.taskScheduler.scheduleTask({
        type: 'SYSTEM_HEALTH_CHECK',
        priority: 8,
        payload: { timestamp: Date.now() }
      });
    }, 30000);

    // FORTRESS v6.1: Enhanced performance metrics every 5 minutes
    setInterval(() => {
      this.emitEnhancedPerformanceMetrics();
    }, 300000);
    
    // Whale balance checks every 2 minutes
    if (process.env.WHALE_WATCHLIST) {
      const whales = process.env.WHALE_WATCHLIST.split(',');
      setInterval(() => {
        whales.forEach(whaleAddress => {
          this.taskScheduler.scheduleTask({
            type: 'WHALE_BALANCE_CHECK',
            priority: 6,
            payload: {
              whaleAddress: whaleAddress.trim(),
              apiKey: process.env.ETHERSCAN_API_KEY
            }
          });
        });
      }, 120000);
    }
  }

  /**
   * CRUCIBLE MANDATE 2: Perform market classification with real market data
   */
  async performMarketClassification() {
    if (!this.marketClassifier || this.systemHealth.marketClassifier !== 'ONLINE') {
      return;
    }

    try {
      // Fetch current market data from Binance
      const marketData = await this.fetchMarketData();

      if (marketData) {
        // Perform classification with real data
        const classification = this.marketClassifier.classifyMarketCondition(marketData);

        // CRUCIBLE MANDATE 4: Track component activity
        this.updateComponentActivity('marketClassifier', true, false);

        // RED TEAM MANDATE 5: Update sanity check metrics
        this.sanityCheck.totalClassifications++;

        if (classification) {
          this.sanityCheck.totalRegimeDetections++;
          this.sanityCheck.lastRegimeDetection = Date.now();

          this.logger.info('market_regime_detected', {
            regime: classification.type,
            confidence: classification.confidence,
            timestamp: classification.timestamp
          });
        }

        // RED TEAM MANDATE 5: Perform sanity check
        await this.performSanityCheck();
      }
    } catch (error) {
      this.logger.error('market_classification_failed', {
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * CRUCIBLE MANDATE 2: Fetch real market data for classification
   */
  async fetchMarketData() {
    try {
      // CRUCIBLE MANDATE 3: Use resilient API client with circuit breaker

      // Fetch order book data with resilience
      const orderBook = await this.apiClient.request({
        url: `/api/v3/depth?symbol=${this.config.symbol}&limit=50`,
        method: 'GET',
        providers: ['binance', 'binanceBackup']
      });

      // Fetch 24hr ticker for price and momentum with resilience
      const ticker = await this.apiClient.request({
        url: `/api/v3/ticker/24hr?symbol=${this.config.symbol}`,
        method: 'GET',
        providers: ['binance', 'binanceBackup']
      });

      // Calculate basic metrics
      const price = parseFloat(ticker.lastPrice);
      const momentum = parseFloat(ticker.priceChangePercent);

      // Calculate pressure (simplified ask/bid ratio)
      const topBid = parseFloat(orderBook.bids[0][0]);
      const topAsk = parseFloat(orderBook.asks[0][0]);
      const pressure = topAsk / topBid;

      // Use liquidity analyzer if available, otherwise calculate basic DLS
      let dlsScore = 50; // Default neutral score
      if (this.liquidityAnalyzer) {
        const analysis = await this.liquidityAnalyzer.analyzeOrderBook({
          bids: orderBook.bids,
          asks: orderBook.asks,
          timestamp: Date.now()
        });
        dlsScore = analysis.dlsScore || 50;
      }

      return {
        price,
        dlsScore,
        pressure,
        momentum,
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.warn('market_data_fetch_failed', {
        error: error.message,
        symbol: this.config.symbol
      });
      return null;
    }
  }

  /**
   * CRUCIBLE MANDATE 4: Update component activity and realistic status
   */
  updateComponentActivity(componentName, isActive = true, hasErrors = false) {
    this.lastActivity[componentName] = Date.now();

    if (hasErrors) {
      this.systemHealth[componentName] = 'DEGRADED';
    } else if (isActive) {
      this.systemHealth[componentName] = 'ONLINE';
    }
  }

  /**
   * CRUCIBLE MANDATE 4: Check for stalled components
   */
  checkForStalledComponents() {
    const now = Date.now();
    const stalledThreshold = 120000; // 2 minutes

    for (const [component, lastActivity] of Object.entries(this.lastActivity)) {
      if (now - lastActivity > stalledThreshold && this.systemHealth[component] === 'ONLINE') {
        this.systemHealth[component] = 'STALLED';
        this.logger.warn('component_stalled', {
          component,
          lastActivity: new Date(lastActivity).toISOString(),
          stalledDuration: now - lastActivity
        });
      }
    }
  }

  /**
   * FORTRESS v6.1: Emit engine heartbeat
   */
  emitHeartbeat() {
    // CRUCIBLE MANDATE 4: Check for stalled components before reporting
    this.checkForStalledComponents();

    const activeComponents = Object.values(this.systemHealth).filter(status => status === 'ONLINE').length;
    const totalComponents = Object.keys(this.systemHealth).length;

    const heartbeat = {
      logType: 'HEARTBEAT',
      status: this.isRunning ? 'OPERATIONAL' : 'OFFLINE',
      timestamp: new Date().toISOString(),
      uptime: Math.floor((Date.now() - this.startTime) / 1000),
      systemHealth: this.systemHealth,
      healthSummary: `${activeComponents}/${totalComponents} components online`,
      // CRUCIBLE MANDATE 4: Remove fantasy metrics, report real data
      metrics: {
        whaleIntents: this.metrics.whaleIntentsDetected,
        liquidityValidations: this.metrics.liquidityValidations,
        derivativesUpdates: this.metrics.derivativesUpdates,
        tasksExecuted: this.metrics.tasksExecuted
      }
    };

    this.logger.info('engine_heartbeat', heartbeat);
  }

  /**
   * FORTRESS v6.1: Emit enhanced performance metrics
   */
  emitEnhancedPerformanceMetrics() {
    const derivativesData = this.derivativesMonitor?.getData() || {};
    const oiDelta = derivativesData.openInterest?.changeRate || 0;
    const fundingRate = derivativesData.fundingRates?.current || 0;

    const enhancedMetrics = {
      WhaleIntents: this.metrics.whaleIntentsDetected,
      LiqDetections: this.metrics.liquidityValidations,
      SigDetections: this.sanityCheck.totalRegimeDetections, // RED TEAM MANDATE 5: Use actual regime detections
      Tasks: this.metrics.tasksExecuted,
      OI_Delta_1m: oiDelta > 0 ? `+${(oiDelta * 1000000).toFixed(1)}M` : `${(oiDelta * 1000000).toFixed(1)}M`,
      Funding_Rate: `${(fundingRate * 100).toFixed(3)}%`,
      // RED TEAM MANDATE 5: Add sanity check status
      SanityStatus: this.sanityCheck.emergencyShutdownTriggered ? 'EMERGENCY_SHUTDOWN' :
                   this.sanityCheck.totalRegimeDetections === 0 && this.sanityCheck.totalClassifications > 50 ? 'WARNING' : 'OK'
    };

    console.log(`📊 PERFORMANCE: WhaleIntents: ${enhancedMetrics.WhaleIntents}, LiqDetections: ${enhancedMetrics.LiqDetections}, SigDetections: ${enhancedMetrics.SigDetections}, Tasks: ${enhancedMetrics.Tasks}, OI_Delta_1m: "${enhancedMetrics.OI_Delta_1m}", Funding_Rate: "${enhancedMetrics.Funding_Rate}", SanityStatus: ${enhancedMetrics.SanityStatus}`);
  }

  /**
   * RED TEAM MANDATE 5: Master-level sanity check system
   */
  async performSanityCheck() {
    if (!this.sanityCheck.enabled || this.sanityCheck.emergencyShutdownTriggered) {
      return;
    }

    const { totalClassifications, totalRegimeDetections, classificationThreshold } = this.sanityCheck;

    // Check if we've hit the threshold with zero detections
    if (totalClassifications >= classificationThreshold && totalRegimeDetections === 0) {
      this.logger.error('sanity_check_failure', {
        message: 'CRITICAL: Zero regime detections after threshold reached',
        totalClassifications,
        classificationThreshold,
        totalRegimeDetections,
        action: 'INITIATING_EMERGENCY_SHUTDOWN'
      });

      await this.triggerEmergencyShutdown();
      return;
    }

    // Warning at 50% of threshold
    const warningThreshold = Math.floor(classificationThreshold / 2);
    if (totalClassifications === warningThreshold && totalRegimeDetections === 0) {
      this.logger.warn('sanity_check_warning', {
        message: 'WARNING: No regime detections at 50% threshold',
        totalClassifications,
        classificationThreshold,
        remainingAttempts: classificationThreshold - totalClassifications,
        recommendation: 'Check classifier thresholds and market data quality'
      });

      // Send Telegram alert
      if (this.telegramReporter) {
        await this.telegramReporter.sendAlert({
          type: 'SANITY_CHECK_WARNING',
          title: '⚠️ SANITY CHECK WARNING',
          message: `No trading signals detected after ${totalClassifications} classifications.\nRemaining attempts: ${classificationThreshold - totalClassifications}\nRecommendation: Check classifier configuration`,
          priority: 'HIGH'
        });
        this.sanityCheck.alertsSent++;
      }
    }
  }

  /**
   * RED TEAM MANDATE 5: Emergency shutdown procedure
   */
  async triggerEmergencyShutdown() {
    this.sanityCheck.emergencyShutdownTriggered = true;

    this.logger.error('emergency_shutdown_initiated', {
      reason: 'Sanity check failure - zero regime detections',
      totalClassifications: this.sanityCheck.totalClassifications,
      uptime: Math.floor((Date.now() - this.startTime) / 1000),
      action: 'HALTING_ENGINE_OPERATIONS'
    });

    // Send critical Telegram alert
    if (this.telegramReporter) {
      await this.telegramReporter.sendAlert({
        type: 'EMERGENCY_SHUTDOWN',
        title: '🚨 EMERGENCY SHUTDOWN',
        message: `CRITICAL FAILURE: Zero trading signals detected after ${this.sanityCheck.totalClassifications} classifications.\n\nEngine halted for manual inspection.\nUptime: ${Math.floor((Date.now() - this.startTime) / 1000)}s\n\nREQUIRES IMMEDIATE ATTENTION`,
        priority: 'CRITICAL'
      });
    }

    // Stop accepting new classifications
    this.systemHealth.marketClassifier = 'EMERGENCY_SHUTDOWN';

    // Log final state
    console.error('🚨 EMERGENCY SHUTDOWN: Engine halted due to sanity check failure');
    console.error(`📊 Final Stats: ${this.sanityCheck.totalClassifications} classifications, ${this.sanityCheck.totalRegimeDetections} regime detections`);

    // Note: Don't call full shutdown() to allow manual inspection
    // Operators can manually restart after investigating the issue
  }

  /**
   * Validate trading signal using dynamic liquidity analysis
   */
  async validateSignal(signal, orderBookData) {
    const analysis = await this.liquidityAnalyzer.analyzeOrderBook(orderBookData);
    
    this.logger.info('signal_validation', {
      signalId: signal.id,
      dls: analysis.dls,
      percentile: analysis.percentile,
      isValid: analysis.isValidForSignal
    });
    
    return {
      isValid: analysis.isValidForSignal,
      liquidityScore: analysis.dls,
      liquidityPercentile: analysis.percentile,
      liquidityRegime: analysis.regime
    };
  }

  /**
   * Get system metrics
   */
  getMetrics() {
    const uptime = this.isRunning ? Date.now() - this.startTime : 0;

    return {
      version: this.version,
      isRunning: this.isRunning,
      uptime: Math.floor(uptime / 1000),
      systemHealth: this.systemHealth,
      metrics: {
        ...this.metrics,
        systemUptime: Math.floor(uptime / 1000)
      },
      components: {
        liquidityAnalyzer: this.liquidityAnalyzer?.getStats(),
        mempoolStreamer: this.mempoolStreamer?.getStats(),
        derivativesMonitor: this.derivativesMonitor?.getStats(),
        taskScheduler: this.taskScheduler?.getStats()
      },
      // RED TEAM MANDATE 5: Include sanity check status
      sanityCheck: {
        enabled: this.sanityCheck.enabled,
        totalClassifications: this.sanityCheck.totalClassifications,
        totalRegimeDetections: this.sanityCheck.totalRegimeDetections,
        successRate: this.sanityCheck.totalClassifications > 0 ?
          ((this.sanityCheck.totalRegimeDetections / this.sanityCheck.totalClassifications) * 100).toFixed(2) + '%' : '0%',
        emergencyShutdownTriggered: this.sanityCheck.emergencyShutdownTriggered,
        status: this.sanityCheck.emergencyShutdownTriggered ? 'EMERGENCY_SHUTDOWN' :
                this.sanityCheck.totalRegimeDetections === 0 && this.sanityCheck.totalClassifications > 50 ? 'WARNING' : 'OK'
      },
      mandatesImplemented: 5,
      strategicViability: this.sanityCheck.emergencyShutdownTriggered ? 'CRITICAL_FAILURE' :
                         this.sanityCheck.totalRegimeDetections > 0 ? 'CONFIRMED' : 'UNDER_EVALUATION'
    };
  }

  /**
   * Shutdown Phoenix Engine
   */
  async shutdown() {
    if (!this.isRunning) return;

    this.logger.info('phoenix_shutdown', 'Initiating graceful shutdown sequence');

    try {
      // Phase 1: Stop accepting new work
      this.logger.info('shutdown_phase_1', 'Stopping task scheduler from accepting new tasks');
      if (this.taskScheduler) {
        await this.taskScheduler.stopAcceptingTasks();
      }

      // Phase 2: Wait for active tasks to complete (10 second timeout)
      this.logger.info('shutdown_phase_2', 'Waiting for active tasks to complete');
      if (this.taskScheduler) {
        await this.taskScheduler.waitForActiveTasks(10000);
      }

      // Phase 3: Terminate all workers with confirmation
      this.logger.info('shutdown_phase_3', 'Terminating worker processes');
      if (this.taskScheduler) {
        await this.taskScheduler.terminateAllWorkers();
      }

      // Phase 4: Disconnect WebSocket clients
      this.logger.info('shutdown_phase_4', 'Disconnecting real-time feeds');
      if (this.mempoolStreamer) {
        await this.mempoolStreamer.stop();
      }

      if (this.derivativesMonitor) {
        await this.derivativesMonitor.stop();
      }

      // CRUCIBLE MANDATE 2: Shutdown market classifier
      if (this.marketClassifier) {
        this.marketClassifier.shutdown();
      }

      // CRUCIBLE MANDATE 3: Shutdown resilient API client
      if (this.apiClient) {
        this.apiClient.shutdown();
      }

      // Phase 5: Stop Express server
      this.logger.info('shutdown_phase_5', 'Stopping Express server');
      if (this.server) {
        await new Promise((resolve) => {
          this.server.close(() => {
            this.logger.info('express_server_stopped', 'Express server closed');
            resolve();
          });
        });
      }

      // Phase 6: Send final notification
      this.logger.info('shutdown_phase_6', 'Sending shutdown notification');
      await this.telegramReporter.sendAlert({
        type: 'SYSTEM_SHUTDOWN',
        title: '🛑 PHOENIX ENGINE SHUTDOWN',
        message: `System shutdown initiated\nUptime: ${Math.floor((Date.now() - this.startTime) / 1000)}s\nShutdown sequence completed`,
        priority: 'NORMAL'
      });

      this.isRunning = false;

      // Phase 7: Final logger shutdown
      // CRUCIBLE MANDATE 4: Report factual shutdown status, not false claims
      this.logger.info('shutdown_complete', 'Shutdown sequence completed');
      this.logger.shutdown();

      // RED TEAM MANDATE 4: Professional status reporting only
      console.log('SentryCoin Engine shutdown sequence completed');

    } catch (error) {
      console.error('❌ Error during shutdown:', error.message);
      // Force exit if graceful shutdown fails
      process.exit(1);
    }
  }
}
